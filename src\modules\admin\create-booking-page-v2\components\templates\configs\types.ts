import type { BookingConfig, PageInfo } from '../../../types'

// Base Configuration Props
export interface BaseConfigProps {
  config: BookingConfig
  pageInfo: PageInfo
  onConfigChange: (updates: Partial<BookingConfig>) => void
  onPageInfoChange: (updates: Partial<PageInfo>) => void
}

// Configuration Component Props - Simplified (no template-specific config needed)
export interface ModernSportConfigProps extends BaseConfigProps {}

export interface ClassicSportConfigProps extends BaseConfigProps {}
