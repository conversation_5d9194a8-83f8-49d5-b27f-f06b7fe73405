'use client'

import type { ContactSectionProps } from './types'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { cn } from '@/lib/utils'
import { Facebook, Globe, Instagram, Mail, MapPin, Phone } from 'lucide-react'
import React from 'react'

const ContactSection: React.FC<ContactSectionProps> = ({
  config,
  pageInfo,
  previewMode = 'desktop',
  className,
  phone,
  email,
  address,
  socialLinks,
}) => {
  const isMobile = previewMode === 'mobile'

  // Use config values or fallback to props or defaults
  const contactPhone = config.contactInfo?.phone || phone || '0949 029 965'
  const contactEmail = config.contactInfo?.email || email || '<EMAIL>'
  const contactAddress = config.contactInfo?.address || address || 'Số 40, đường số 11, phường Trường Thọ, TP.Th<PERSON>, TP.HCM'
  const contactSocialLinks = {
    facebook: config.contactInfo?.socialLinks?.facebook || socialLinks?.facebook || 'https://facebook.com/booking-easy',
    instagram: config.contactInfo?.socialLinks?.instagram || socialLinks?.instagram || 'https://instagram.com/booking-easy',
    website: config.contactInfo?.socialLinks?.website || socialLinks?.website || 'https://booking-easy.com',
  }

  const contactItems = [
    {
      icon: Phone,
      label: 'Điện thoại',
      value: contactPhone,
      href: `tel:${contactPhone}`,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
    },
    {
      icon: Mail,
      label: 'Email',
      value: contactEmail,
      href: `mailto:${contactEmail}`,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
    },
    {
      icon: MapPin,
      label: 'Địa chỉ',
      value: contactAddress,
      href: `https://maps.google.com/?q=${encodeURIComponent(contactAddress)}`,
      color: 'text-red-600',
      bgColor: 'bg-red-100',
    },
  ]

  const socialItems = [
    {
      icon: Facebook,
      label: 'Facebook',
      href: contactSocialLinks.facebook,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
    },
    {
      icon: Instagram,
      label: 'Instagram',
      href: contactSocialLinks.instagram,
      color: 'text-pink-600',
      bgColor: 'bg-pink-100',
    },
    {
      icon: Globe,
      label: 'Website',
      href: contactSocialLinks.website,
      color: 'text-gray-600',
      bgColor: 'bg-gray-100',
    },
  ].filter(item => item.href && item.href !== 'https://facebook.com/booking-easy' && item.href !== 'https://instagram.com/booking-easy' && item.href !== 'https://booking-easy.com')

  return (
    <div className={cn('space-y-6', className)}>
      <div>
        <h2 className={cn(
          'font-bold text-gray-900 mb-4',
          isMobile ? 'text-lg' : 'text-xl',
        )}
        >
          Thông tin liên hệ
        </h2>
      </div>

      <div className={cn(
        'grid gap-6',
        isMobile ? 'grid-cols-1' : 'grid-cols-2',
      )}
      >
        {/* Contact Information */}
        <div className="space-y-4">
          <h3 className={cn(
            'font-semibold text-gray-900',
            isMobile ? 'text-base' : 'text-lg',
          )}
          >
            Liên hệ trực tiếp
          </h3>

          <div className="space-y-3">
            {contactItems.map((item, index) => (
              <Card key={index} className="border-orange-200 hover:shadow-md transition-shadow">
                <CardContent className="p-4">
                  <a
                    href={item.href}
                    target={item.label === 'Địa chỉ' ? '_blank' : undefined}
                    rel={item.label === 'Địa chỉ' ? 'noopener noreferrer' : undefined}
                    className="flex items-start gap-3 hover:opacity-80 transition-opacity"
                  >
                    <div className={cn(
                      'p-2 rounded-lg flex-shrink-0',
                      item.bgColor,
                    )}
                    >
                      <item.icon className={cn('w-5 h-5', item.color)} />
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className={cn(
                        'font-medium text-gray-900',
                        isMobile ? 'text-sm' : 'text-base',
                      )}
                      >
                        {item.label}
                      </div>
                      <div className={cn(
                        'text-gray-600 break-words',
                        isMobile ? 'text-xs' : 'text-sm',
                      )}
                      >
                        {item.value}
                      </div>
                    </div>
                  </a>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Social Links & Quick Actions */}
        <div className="space-y-4">
          <h3 className={cn(
            'font-semibold text-gray-900',
            isMobile ? 'text-base' : 'text-lg',
          )}
          >
            Kết nối với chúng tôi
          </h3>

          {/* Social Links */}
          {socialItems.length > 0 && (
            <div className="space-y-3">
              {socialItems.map((item, index) => (
                <Card key={index} className="border-orange-200 hover:shadow-md transition-shadow">
                  <CardContent className="p-4">
                    <a
                      href={item.href}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center gap-3 hover:opacity-80 transition-opacity"
                    >
                      <div className={cn(
                        'p-2 rounded-lg flex-shrink-0',
                        item.bgColor,
                      )}
                      >
                        <item.icon className={cn('w-5 h-5', item.color)} />
                      </div>
                      <div className="flex-1">
                        <div className={cn(
                          'font-medium text-gray-900',
                          isMobile ? 'text-sm' : 'text-base',
                        )}
                        >
                          {item.label}
                        </div>
                        <div className={cn(
                          'text-gray-600',
                          isMobile ? 'text-xs' : 'text-sm',
                        )}
                        >
                          Theo dõi để cập nhật thông tin mới
                        </div>
                      </div>
                    </a>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}

          {/* Quick Actions */}
          <div className="space-y-3">
            <Button
              className="w-full bg-orange-500 hover:bg-orange-600 text-white"
              size={isMobile ? 'sm' : 'default'}
            >
              <Phone className="w-4 h-4 mr-2" />
              Gọi ngay để đặt sân
            </Button>

            <Button
              variant="outline"
              className="w-full border-orange-300 text-orange-600 hover:bg-orange-50"
              size={isMobile ? 'sm' : 'default'}
            >
              <Mail className="w-4 h-4 mr-2" />
              Gửi email tư vấn
            </Button>
          </div>

          {/* Operating Hours */}
          <Card className="border-orange-200 bg-orange-50">
            <CardContent className="p-4">
              <h4 className={cn(
                'font-medium text-gray-900 mb-2',
                isMobile ? 'text-sm' : 'text-base',
              )}
              >
                Giờ hoạt động
              </h4>
              <div className={cn(
                'text-gray-600 space-y-1',
                isMobile ? 'text-xs' : 'text-sm',
              )}
              >
                <div>
                  Thứ 2 - Thứ 6:
                  {config.openTime || '06:00'}
                  {' '}
                  -
                  {config.closeTime || '22:00'}
                </div>
                <div>
                  Thứ 7 - Chủ nhật:
                  {config.openTime || '06:00'}
                  {' '}
                  -
                  {config.closeTime || '22:00'}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

export default ContactSection
