'use client'

import type { BaseLayoutProps } from './layouts/types'
import { cn } from '@/lib/utils'
import React from 'react'
import {
  BannerSection,
  ContactSection,
  DescriptionSection,
  GridSlotSection,
  MapSection,
} from './layouts'

interface ModernSportTemplateProps extends BaseLayoutProps {
  selectedDate?: Date
  onDateChange?: (date: Date) => void
  onSlotSelect?: (fieldId: string, timeSlot: string) => void
  selectedSlots?: Record<string, string[]>
}

/**
 * Modern Sport Template Component
 *
 * Layout structure:
 * -----------------
 * |Banner image   |
 * -----------------
 * |description|select date|
 * |show info booking|grid slot|
 * |contact|map|
 */
const ModernSportTemplate: React.FC<ModernSportTemplateProps> = React.memo(({
  config,
  pageInfo,
  previewMode = 'desktop',
  className,
  selectedDate,
  onDateChange,
  onSlotSelect,
  selectedSlots,
}) => {
  const isMobile = previewMode === 'mobile'

  return (
    <div className={cn(
      'w-full space-y-6',
      isMobile ? 'p-4' : 'p-6',
      className,
    )}
    >
      {/* Banner Section - Full Width */}
      <BannerSection
        config={config}
        pageInfo={pageInfo}
        previewMode={previewMode}
      />

      {/* Description & Date Selection Row */}
      <div className={cn(
        'grid gap-6',
        isMobile ? 'grid-cols-1' : 'grid-cols-1',
      )}
      >
        <DescriptionSection
          config={config}
          pageInfo={pageInfo}
          previewMode={previewMode}
        />
      </div>

      {/* Booking Info & Grid Slot Row */}
      <div className={cn(
        'grid gap-6',
        isMobile ? 'grid-cols-1' : 'grid-cols-1',
      )}
      >
        {/* Grid Slot - 3 columns */}
        <div className={cn(
          isMobile ? 'col-span-1' : 'col-span-3',
        )}
        >
          <GridSlotSection
            config={config}
            pageInfo={pageInfo}
            previewMode={previewMode}
            selectedDate={selectedDate}
            onDateChange={onDateChange}
            onSlotSelect={onSlotSelect}
            selectedSlots={selectedSlots}
          />
        </div>
      </div>

      {/* Contact & Map Row */}
      <div className={cn(
        'grid gap-6',
        isMobile ? 'grid-cols-1' : 'grid-cols-2',
      )}
      >
        {/* Contact Section */}
        <ContactSection
          config={config}
          pageInfo={pageInfo}
          previewMode={previewMode}
        />

        {/* Map Section */}
        <MapSection
          config={config}
          pageInfo={pageInfo}
          previewMode={previewMode}
          showDirections={true}
        />
      </div>
    </div>
  )
})

export default ModernSportTemplate
