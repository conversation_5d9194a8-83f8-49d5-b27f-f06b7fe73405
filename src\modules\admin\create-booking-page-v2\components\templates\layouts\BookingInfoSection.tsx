'use client'

import type { BookingInfoSectionProps } from './types'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent } from '@/components/ui/card'
import { cn } from '@/lib/utils'
import { Clock, DollarSign, Trophy, Users } from 'lucide-react'
import React from 'react'

const BookingInfoSection: React.FC<BookingInfoSectionProps> = ({
  config,
  pageInfo,
  previewMode = 'desktop',
  className,
  showPricing = true,
  showCapacity = true,
  showFieldTypes = true,
}) => {
  const isMobile = previewMode === 'mobile'

  const getFieldIcon = (type: string) => {
    switch (type) {
      case 'football': return '⚽'
      case 'tennis': return '🎾'
      case 'badminton': return '🏸'
      case 'basketball': return '🏀'
      default: return '🏟️'
    }
  }

  const getFieldName = (type: string) => {
    switch (type) {
      case 'football': return 'Bóng đá'
      case 'tennis': return 'Tennis'
      case 'badminton': return 'Cầu lông'
      case 'basketball': return 'Bóng rổ'
      default: return 'Thể thao'
    }
  }

  return (
    <div className={cn('space-y-6', className)}>
      <div>
        <h2 className={cn(
          'font-bold text-gray-900 mb-4',
          isMobile ? 'text-lg' : 'text-xl',
        )}
        >
          Thông tin đặt sân
        </h2>
      </div>

      {/* Summary Stats */}
      <div className={cn(
        'grid gap-4',
        isMobile ? 'grid-cols-2' : 'grid-cols-4',
      )}
      >
        <Card className="border-orange-200">
          <CardContent className="p-4 text-center">
            <Trophy className="w-6 h-6 text-orange-500 mx-auto mb-2" />
            <div className={cn(
              'font-bold text-gray-900',
              isMobile ? 'text-lg' : 'text-xl',
            )}
            >
              {config.fields.length}
            </div>
            <div className={cn(
              'text-gray-600',
              isMobile ? 'text-xs' : 'text-sm',
            )}
            >
              Sân có sẵn
            </div>
          </CardContent>
        </Card>

        <Card className="border-orange-200">
          <CardContent className="p-4 text-center">
            <Clock className="w-6 h-6 text-orange-500 mx-auto mb-2" />
            <div className={cn(
              'font-bold text-gray-900',
              isMobile ? 'text-lg' : 'text-xl',
            )}
            >
              16h
            </div>
            <div className={cn(
              'text-gray-600',
              isMobile ? 'text-xs' : 'text-sm',
            )}
            >
              Giờ hoạt động
            </div>
          </CardContent>
        </Card>

        {(showCapacity && config.showCapacity) && (
          <Card className="border-orange-200">
            <CardContent className="p-4 text-center">
              <Users className="w-6 h-6 text-orange-500 mx-auto mb-2" />
              <div className={cn(
                'font-bold text-gray-900',
                isMobile ? 'text-lg' : 'text-xl',
              )}
              >
                {config.fields.length > 0 ? Math.max(...config.fields.map(f => f.capacity), 0) : 0}
              </div>
              <div className={cn(
                'text-gray-600',
                isMobile ? 'text-xs' : 'text-sm',
              )}
              >
                Người/sân
              </div>
            </CardContent>
          </Card>
        )}

        {(showPricing && config.pricing?.showPricing) && (
          <Card className="border-orange-200">
            <CardContent className="p-4 text-center">
              <DollarSign className="w-6 h-6 text-orange-500 mx-auto mb-2" />
              <div className={cn(
                'font-bold text-gray-900',
                isMobile ? 'text-lg' : 'text-xl',
              )}
              >
                {config.pricing.basePrice ?
                  `${(config.pricing.basePrice / 1000).toFixed(0)}K` :
                  '300K'
                }
              </div>
              <div className={cn(
                'text-gray-600',
                isMobile ? 'text-xs' : 'text-sm',
              )}
              >
                {config.pricing.currency || 'VNĐ'}/
                {config.pricing.priceUnit === 'hour' ? 'giờ' :
                 config.pricing.priceUnit === 'session' ? 'buổi' :
                 config.pricing.priceUnit === 'day' ? 'ngày' : 'giờ'}
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Field List */}
      {(showFieldTypes && config.showFieldTypes) && config.fields.length > 0 && (
        <div>
          <h3 className={cn(
            'font-semibold text-gray-900 mb-3',
            isMobile ? 'text-base' : 'text-lg',
          )}
          >
            Danh sách sân (
            {config.fields.length}
            )
          </h3>

          <div className={cn(
            'grid gap-3',
            isMobile ? 'grid-cols-1' : 'grid-cols-2',
          )}
          >
            {config.fields.map((field, index) => (
              <Card key={field.id} className="border-orange-200 hover:shadow-md transition-shadow">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <span className="text-2xl">{getFieldIcon(field.type)}</span>
                      <div>
                        <h4 className={cn(
                          'font-medium text-gray-900',
                          isMobile ? 'text-sm' : 'text-base',
                        )}
                        >
                          {field.name}
                        </h4>
                        <p className={cn(
                          'text-gray-600',
                          isMobile ? 'text-xs' : 'text-sm',
                        )}
                        >
                          {getFieldName(field.type)}
                        </p>
                      </div>
                    </div>

                    <div className="text-right">
                      <Badge variant="secondary" className="bg-orange-100 text-orange-700">
                        {field.capacity}
                        {' '}
                        người
                      </Badge>
                      <div className={cn(
                        'text-orange-600 font-medium mt-1',
                        isMobile ? 'text-xs' : 'text-sm',
                      )}
                      >
                        300K/giờ
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

export default BookingInfoSection
