'use client'

import React, { useState } from 'react'
import { MapPin, Navigation, ExternalLink, Copy, Check } from 'lucide-react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import type { MapSectionProps } from './types'

const MapSection: React.FC<MapSectionProps> = ({
  config,
  pageInfo,
  previewMode = 'desktop',
  className,
  latitude = 10.8231,
  longitude = 106.6297,
  address,
  showDirections = true,
}) => {
  const [copied, setCopied] = useState(false)
  const isMobile = previewMode === 'mobile'

  // Use config values or fallback to props or defaults
  const mapAddress = config.contactInfo?.address || address || 'Số 40, đường số 11, phường Trường Thọ, TP.Thủ Đức, TP.HCM'
  const shouldShowDirections = config.showDirections !== undefined ? config.showDirections : showDirections

  const handleCopyAddress = async () => {
    try {
      await navigator.clipboard.writeText(mapAddress)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error('Failed to copy address:', err)
    }
  }

  const handleGetDirections = () => {
    const googleMapsUrl = `https://www.google.com/maps/dir/?api=1&destination=${latitude},${longitude}`
    window.open(googleMapsUrl, '_blank')
  }

  const handleOpenInMaps = () => {
    const googleMapsUrl = `https://www.google.com/maps/search/?api=1&query=${latitude},${longitude}`
    window.open(googleMapsUrl, '_blank')
  }

  // Generate Google Maps embed URL
  const mapEmbedUrl = `https://www.google.com/maps/embed/v1/place?key=YOUR_API_KEY&q=${latitude},${longitude}&zoom=16`
  
  // For demo purposes, we'll use a placeholder map
  const placeholderMapUrl = `https://api.mapbox.com/styles/v1/mapbox/streets-v11/static/pin-s-l+ff6b35(${longitude},${latitude})/${longitude},${latitude},15,0/600x400@2x?access_token=pk.eyJ1IjoibWFwYm94IiwiYSI6ImNpejY4NXVycTA2emYycXBndHRqcmZ3N3gifQ.rJcFIG214AriISLbB6B5aw`

  return (
    <div className={cn('space-y-6', className)}>
      <div className="flex items-center justify-between">
        <h2 className={cn(
          'font-bold text-gray-900',
          isMobile ? 'text-lg' : 'text-xl'
        )}>
          Vị trí & Bản đồ
        </h2>
        <Badge variant="secondary" className="bg-orange-100 text-orange-700">
          <MapPin className="w-3 h-3 mr-1" />
          Dễ tìm
        </Badge>
      </div>

      <div className={cn(
        'grid gap-6',
        isMobile ? 'grid-cols-1' : 'grid-cols-3'
      )}>
        {/* Map Display */}
        <div className={cn(
          'space-y-4',
          isMobile ? 'col-span-1' : 'col-span-2'
        )}>
          <Card className="border-orange-200 overflow-hidden">
            <div className="relative">
              {/* Map Placeholder */}
              <div 
                className={cn(
                  'bg-gray-200 bg-cover bg-center relative',
                  isMobile ? 'h-48' : 'h-64'
                )}
                style={{
                  backgroundImage: `url(${placeholderMapUrl})`,
                }}
              >
                {/* Map Overlay */}
                <div className="absolute inset-0 bg-black/10" />
                
                {/* Center Pin */}
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                  <div className="relative">
                    <div className="w-8 h-8 bg-orange-500 rounded-full border-4 border-white shadow-lg flex items-center justify-center">
                      <MapPin className="w-4 h-4 text-white" />
                    </div>
                    <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-1">
                      <div className="bg-white px-2 py-1 rounded shadow-lg text-xs font-medium text-gray-900 whitespace-nowrap">
                        {pageInfo.name || 'Sân thể thao'}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Map Controls */}
                <div className="absolute top-4 right-4 space-y-2">
                  <Button
                    size="sm"
                    variant="secondary"
                    className="bg-white/90 hover:bg-white shadow-md"
                    onClick={handleOpenInMaps}
                  >
                    <ExternalLink className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>
          </Card>

          {/* Quick Actions */}
          {shouldShowDirections && (
            <div className={cn(
              'grid gap-3',
              isMobile ? 'grid-cols-1' : 'grid-cols-2'
            )}>
              <Button
                onClick={handleGetDirections}
                className="bg-orange-500 hover:bg-orange-600 text-white"
                size={isMobile ? 'sm' : 'default'}
              >
                <Navigation className="w-4 h-4 mr-2" />
                Chỉ đường
              </Button>
              
              <Button
                onClick={handleOpenInMaps}
                variant="outline"
                className="border-orange-300 text-orange-600 hover:bg-orange-50"
                size={isMobile ? 'sm' : 'default'}
              >
                <ExternalLink className="w-4 h-4 mr-2" />
                Mở bản đồ
              </Button>
            </div>
          )}
        </div>

        {/* Location Info */}
        <div className="space-y-4">
          <Card className="border-orange-200">
            <CardContent className="p-4 space-y-4">
              <div>
                <h3 className={cn(
                  'font-semibold text-gray-900 mb-2',
                  isMobile ? 'text-base' : 'text-lg'
                )}>
                  Địa chỉ
                </h3>
                <p className={cn(
                  'text-gray-600 leading-relaxed',
                  isMobile ? 'text-sm' : 'text-base'
                )}>
                  {mapAddress}
                </p>
              </div>

              <Button
                onClick={handleCopyAddress}
                variant="outline"
                size="sm"
                className="w-full border-orange-300 text-orange-600 hover:bg-orange-50"
              >
                {copied ? (
                  <>
                    <Check className="w-4 h-4 mr-2" />
                    Đã sao chép
                  </>
                ) : (
                  <>
                    <Copy className="w-4 h-4 mr-2" />
                    Sao chép địa chỉ
                  </>
                )}
              </Button>
            </CardContent>
          </Card>

          {/* Transportation Info */}
          <Card className="border-orange-200 bg-orange-50">
            <CardContent className="p-4">
              <h4 className={cn(
                'font-medium text-gray-900 mb-3',
                isMobile ? 'text-sm' : 'text-base'
              )}>
                Phương tiện di chuyển
              </h4>
              
              <div className={cn(
                'space-y-2 text-gray-600',
                isMobile ? 'text-xs' : 'text-sm'
              )}>
                <div className="flex items-start gap-2">
                  <span className="text-orange-500 font-medium">🚗</span>
                  <span>Có bãi đỗ xe miễn phí</span>
                </div>
                <div className="flex items-start gap-2">
                  <span className="text-orange-500 font-medium">🚌</span>
                  <span>Xe bus: Tuyến 01, 19, 38</span>
                </div>
                <div className="flex items-start gap-2">
                  <span className="text-orange-500 font-medium">🏍️</span>
                  <span>Grab/Be dễ dàng tìm thấy</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Nearby Landmarks */}
          <Card className="border-orange-200">
            <CardContent className="p-4">
              <h4 className={cn(
                'font-medium text-gray-900 mb-3',
                isMobile ? 'text-sm' : 'text-base'
              )}>
                Địa điểm gần đó
              </h4>
              
              <div className={cn(
                'space-y-2 text-gray-600',
                isMobile ? 'text-xs' : 'text-sm'
              )}>
                <div>📍 Vincom Thủ Đức (2km)</div>
                <div>📍 Đại học Quốc gia (1.5km)</div>
                <div>📍 Bệnh viện Thủ Đức (3km)</div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

export default MapSection
