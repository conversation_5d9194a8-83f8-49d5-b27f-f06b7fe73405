'use client'

import type { BookingField, FieldType } from '../../../types'
import type { ClassicSportConfigProps } from './types'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Separator } from '@/components/ui/separator'
import { Clock, Image as ImageIcon, Plus, Settings, Trash2, Users } from 'lucide-react'
import React from 'react'

const FIELD_TYPES: { value: FieldType, label: string, icon: string }[] = [
  { value: 'football', label: 'Bóng đá', icon: '⚽' },
  { value: 'tennis', label: 'Tennis', icon: '🎾' },
  { value: 'badminton', label: 'Cầu lông', icon: '🏸' },
  { value: 'basketball', label: '<PERSON><PERSON>g rổ', icon: '🏀' },
]

const ClassicSportConfig: React.FC<ClassicSportConfigProps> = ({
  config,
  pageInfo,
  onConfigChange,
  onPageInfoChange,
}) => {
  const handleFieldAdd = () => {
    const newField: BookingField = {
      id: `field-${Date.now()}`,
      name: `Sân ${config.fields.length + 1}`,
      type: 'football',
      capacity: 10,
    }
    onConfigChange({
      fields: [...config.fields, newField],
    })
  }

  const handleFieldRemove = (fieldId: string) => {
    onConfigChange({
      fields: config.fields.filter(field => field.id !== fieldId),
    })
  }

  const handleFieldUpdate = (fieldId: string, updates: Partial<BookingField>) => {
    onConfigChange({
      fields: config.fields.map(field =>
        field.id === fieldId ? { ...field, ...updates } : field,
      ),
    })
  }

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      // In real app, upload to server and get URL
      const imageUrl = URL.createObjectURL(file)
      onConfigChange({ bannerImage: imageUrl })
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-3">
        <Settings className="w-6 h-6 text-blue-500" />
        <div>
          <h3 className="text-lg font-semibold text-gray-900">
            Cấu hình sân thể thao
          </h3>
          <p className="text-sm text-gray-600">
            Thiết lập thông tin cơ bản cho trang đặt sân (Giao diện cổ điển)
          </p>
        </div>
      </div>

      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2">
            <Settings className="w-4 h-4" />
            Thông tin cơ bản
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="banner-title">Tiêu đề banner</Label>
            <Input
              id="banner-title"
              value={config.bannerTitle}
              onChange={e => onConfigChange({ bannerTitle: e.target.value })}
              placeholder="VD: CLB Cầu lông B-ZONE 11"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="banner-subtitle">Mô tả ngắn</Label>
            <Input
              id="banner-subtitle"
              value={config.bannerSubtitle}
              onChange={e => onConfigChange({ bannerSubtitle: e.target.value })}
              placeholder="VD: Đặt sân cầu lông chất lượng cao"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="banner-image">Ảnh banner</Label>
            <div className="flex items-center gap-3">
              <Input
                id="banner-image"
                type="file"
                accept="image/*"
                onChange={handleImageUpload}
                className="hidden"
              />
              <Button
                type="button"
                variant="outline"
                onClick={() => document.getElementById('banner-image')?.click()}
                className="flex items-center gap-2"
              >
                <ImageIcon className="w-4 h-4" />
                Chọn ảnh
              </Button>
              {config.bannerImage && (
                <div className="flex items-center gap-2">
                  <img
                    src={config.bannerImage}
                    alt="Banner preview"
                    className="w-16 h-10 object-cover rounded border"
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => onConfigChange({ bannerImage: '' })}
                  >
                    Xóa
                  </Button>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Operating Hours */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2">
            <Clock className="w-4 h-4" />
            Giờ hoạt động
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="open-time">Giờ mở cửa</Label>
              <Input
                id="open-time"
                type="time"
                value={config.openTime}
                onChange={e => onConfigChange({ openTime: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="close-time">Giờ đóng cửa</Label>
              <Input
                id="close-time"
                type="time"
                value={config.closeTime}
                onChange={e => onConfigChange({ closeTime: e.target.value })}
              />
            </div>
          </div>
          <p className="text-sm text-gray-600">
            Lịch đặt sân sẽ hiển thị các khung giờ từ
            {' '}
            {config.openTime}
            {' '}
            đến
            {' '}
            {config.closeTime}
          </p>
        </CardContent>
      </Card>

      {/* Fields Configuration */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2">
            <Users className="w-4 h-4" />
            Cấu hình sân (
            {config.fields.length}
            )
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {config.fields.length === 0
            ? (
                <div className="text-center py-8 text-gray-500">
                  <Users className="w-12 h-12 mx-auto mb-3 opacity-50" />
                  <p>Chưa có sân nào được thêm</p>
                  <p className="text-sm">Nhấn "Thêm sân" để bắt đầu</p>
                </div>
              )
            : (
                <div className="space-y-3">
                  {config.fields.map((field, index) => (
                    <div key={field.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="font-medium text-gray-900">
                          Sân
                          {index + 1}
                        </h4>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => handleFieldRemove(field.id)}
                          className="text-red-600 hover:text-red-700 hover:bg-red-50"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                        <div className="space-y-2">
                          <Label>Tên sân</Label>
                          <Input
                            value={field.name}
                            onChange={e => handleFieldUpdate(field.id, { name: e.target.value })}
                            placeholder="VD: Sân VIP"
                          />
                        </div>

                        <div className="space-y-2">
                          <Label>Loại sân</Label>
                          <Select
                            value={field.type}
                            onValueChange={(value: FieldType) => handleFieldUpdate(field.id, { type: value })}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {FIELD_TYPES.map(type => (
                                <SelectItem key={type.value} value={type.value}>
                                  <div className="flex items-center gap-2">
                                    <span>{type.icon}</span>
                                    <span>{type.label}</span>
                                  </div>
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}

          <Separator />

          <Button
            type="button"
            variant="outline"
            onClick={handleFieldAdd}
            className="w-full border-dashed border-blue-300 text-blue-600 hover:bg-blue-50"
          >
            <Plus className="w-4 h-4 mr-2" />
            Thêm sân mới
          </Button>
        </CardContent>
      </Card>

    </div>
  )
}

export default ClassicSportConfig
