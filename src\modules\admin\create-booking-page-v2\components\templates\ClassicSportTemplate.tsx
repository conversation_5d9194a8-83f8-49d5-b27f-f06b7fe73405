'use client'

import type { BaseLayoutProps } from './layouts/types'
import { cn } from '@/lib/utils'
import React from 'react'
import {
  BannerSection,
  BookingInfoSection,
  ContactSection,
  DescriptionSection,
  GridSlotSection,
  MapSection,
} from './layouts'

interface ClassicSportTemplateProps extends BaseLayoutProps {
  selectedDate?: Date
  onDateChange?: (date: Date) => void
  onSlotSelect?: (fieldId: string, timeSlot: string) => void
  selectedSlots?: Record<string, string[]>
}

/**
 * Classic Sport Template Component
 *
 * Layout structure - Traditional vertical layout:
 * -----------------
 * |Banner image   |
 * -----------------
 * |description    |
 * -----------------
 * |booking info   |
 * -----------------
 * |grid slot      |
 * -----------------
 * |contact        |
 * -----------------
 * |map            |
 * -----------------
 */
const ClassicSportTemplate: React.FC<ClassicSportTemplateProps> = React.memo(({
  config,
  pageInfo,
  previewMode = 'desktop',
  className,
  selectedDate,
  onDateChange,
  onSlotSelect,
  selectedSlots,
}) => {
  const isMobile = previewMode === 'mobile'

  return (
    <div className={cn(
      'w-full space-y-6',
      isMobile ? 'p-4' : 'p-6',
      className,
    )}
    >
      {/* Banner Section */}
      <BannerSection
        config={config}
        pageInfo={pageInfo}
        previewMode={previewMode}
      />

      {/* Description Section */}
      <DescriptionSection
        config={config}
        pageInfo={pageInfo}
        previewMode={previewMode}
      />

      {/* Booking Info Section */}
      <BookingInfoSection
        config={config}
        pageInfo={pageInfo}
        previewMode={previewMode}
        showPricing={true}
        showCapacity={true}
        showFieldTypes={true}
      />

      {/* Grid Slot Section */}
      <GridSlotSection
        config={config}
        pageInfo={pageInfo}
        previewMode={previewMode}
        selectedDate={selectedDate}
        onDateChange={onDateChange}
        onSlotSelect={onSlotSelect}
        selectedSlots={selectedSlots}
      />

      {/* Contact Section */}
      <ContactSection
        config={config}
        pageInfo={pageInfo}
        previewMode={previewMode}
      />

      {/* Map Section */}
      <MapSection
        config={config}
        pageInfo={pageInfo}
        previewMode={previewMode}
        showDirections={true}
      />

      {/* Footer CTA */}
      <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-6 text-center text-white">
        <h3 className={cn(
          'font-bold mb-2',
          isMobile ? 'text-lg' : 'text-xl',
        )}
        >
          Đặt sân dễ dàng
        </h3>
        <p className={cn(
          'mb-4 opacity-90',
          isMobile ? 'text-sm' : 'text-base',
        )}
        >
          Giao diện đơn giản, dễ sử dụng cho mọi lứa tuổi
        </p>
        <button className="bg-white text-blue-600 px-8 py-3 rounded-lg font-medium hover:bg-gray-50 transition-colors">
          Bắt đầu đặt sân
        </button>
      </div>
    </div>
  )
})

export default ClassicSportTemplate
